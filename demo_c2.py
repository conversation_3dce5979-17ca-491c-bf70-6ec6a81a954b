#!/usr/bin/env python3
"""
Demo script to test the complete C2 functionality
"""

import requests
import time
import json

def demo_c2_system():
    """Demonstrate the C2 system functionality"""
    
    print("🎯 C2 System Demo")
    print("=" * 50)
    
    # Wait for server to be ready
    print("1. Checking server status...")
    for i in range(5):
        try:
            response = requests.get("http://127.0.0.1:1488/api/clients", timeout=2)
            if response.status_code == 200:
                print("✅ Server is running!")
                break
        except:
            print(f"   Waiting for server... ({i+1}/5)")
            time.sleep(2)
    else:
        print("❌ Server not responding. Make sure to run: python server.py")
        return
    
    # Check for connected clients
    print("\n2. Checking for connected clients...")
    clients = response.json()
    
    if not clients:
        print("❌ No clients connected.")
        print("   To connect a client, run: python client.py")
        print("   Then run this demo again.")
        return
    
    print(f"✅ Found {len(clients)} connected client(s):")
    for client in clients:
        print(f"   - Client {client['id']}: {client['address']} (Connected: {client['connected_at']})")
    
    # Test command execution
    client_id = clients[0]['id']
    test_commands = ["whoami", "hostname", "pwd"]
    
    print(f"\n3. Testing command execution on Client {client_id}...")
    
    for cmd in test_commands:
        print(f"\n   Executing: {cmd}")
        
        # Send command
        response = requests.post(
            "http://127.0.0.1:1488/api/send_command",
            json={"client_id": client_id, "command": cmd},
            timeout=5
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                command_id = result['command_id']
                print(f"   ✅ Command sent (ID: {command_id})")
                
                # Wait for response
                print("   ⏳ Waiting for response...")
                for i in range(10):
                    time.sleep(1)
                    resp = requests.get(f"http://127.0.0.1:1488/api/get_response/{command_id}")
                    if resp.status_code == 200:
                        data = resp.json()
                        if data.get('status') != 'pending':
                            output = data.get('output', 'No output').strip()
                            print(f"   📥 Output: {output}")
                            break
                    print(f"      Waiting... ({i+1}/10)")
                else:
                    print("   ⏰ Timeout waiting for response")
            else:
                print(f"   ❌ Failed to send command: {result.get('error')}")
        else:
            print(f"   ❌ HTTP error: {response.status_code}")
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed!")
    print("\n💡 Next steps:")
    print("   - Open http://127.0.0.1:1488 in your browser")
    print("   - Use the web dashboard to send commands")
    print("   - Connect more clients with: python client.py")

if __name__ == "__main__":
    demo_c2_system()
